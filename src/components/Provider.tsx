"use client";

import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, ToastProvider, createTheme } from "@design-systems/apollo-ui";
import { ApolloProvider, Theme } from "@apollo/ui";

const Provider = ({ packageName, children }: { packageName: string; children: React.ReactNode }) => {
    const themeProviderProps = packageName === 'design-systems-apollo-ui'
        ? { theme: createTheme() }
        : { theme: createTheme(), children: <ToastProvider><Theme>{children}</Theme></ToastProvider> };

    return (
        <>
            {packageName === 'apollo/ui' ? <ApolloProvider>{children}</ApolloProvider> : <ThemeProvider {...themeProviderProps}>{children}</ThemeProvider>}
        </>
    )
}
export default Provider