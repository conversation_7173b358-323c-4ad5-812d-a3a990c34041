"use client";

import { useRouter } from "next/navigation";

export default function Home() {
  const router = useRouter();


  return (
    <div className="min-h-screen bg-gray-50 p-6">
        {/* Header with Selection */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Apollo UI Package Explorer
          </h1>
          <p className="text-gray-600 mb-6">
            Select a package option to see live examples and components in action.
          </p>
          {/* Package Selection Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => router.push('/apollo-ui')}
              className="px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 bg-white text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400 hover:shadow-lg hover:transform hover:scale-105"
            >
              <div className="text-lg font-bold">@apollo/ui</div>
              <div className="text-sm opacity-90 mt-1">Recommended</div>
            </button>
            <button
              onClick={() => router.push('/design-systems-apollo-ui')}
              className="px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 bg-white text-green-600 border-green-300 hover:bg-green-50 hover:border-green-400 hover:shadow-lg hover:transform hover:scale-105"
            >
              <div className="text-lg font-bold">@design-systems/apollo-ui</div>
            </button>
            <button
              onClick={() => router.push('/combine')}
              className="px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 bg-white text-purple-600 border-purple-300 hover:bg-purple-50 hover:border-purple-400 hover:shadow-lg hover:transform hover:scale-105"
            >
              <div className="text-lg font-bold">Combined</div>
            </button>
        </div>
    </div>
  );
}