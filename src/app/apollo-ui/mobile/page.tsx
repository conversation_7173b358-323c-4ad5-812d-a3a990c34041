"use client";

import { useRouter } from "next/navigation";

export default function ApolloUIMobilePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header with Back Button */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back
          </button>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
              @apollo/ui - Mobile Components
            </h1>
            <p className="text-gray-600 mt-1">
              Touch-optimized and responsive components
            </p>
          </div>
        </div>

        {/* Mobile-First Content */}
        <div className="space-y-6">
          {/* Touch Components */}
          <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Touch-Optimized Components</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg border">
                <div className="font-medium text-gray-900 mb-2">Touch Buttons</div>
                <div className="text-sm text-gray-600 mb-3">Large, finger-friendly buttons</div>
                <button className="w-full py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                  Touch Me
                </button>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg border">
                <div className="font-medium text-gray-900 mb-2">Swipe Cards</div>
                <div className="text-sm text-gray-600 mb-3">Swipeable content cards</div>
                <div className="bg-white p-3 rounded border shadow-sm">
                  <div className="text-sm font-medium">Swipe left/right</div>
                  <div className="text-xs text-gray-500">Card content here</div>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Forms */}
          <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Mobile Forms</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mobile Input
                </label>
                <input
                  type="text"
                  placeholder="Touch to type..."
                  className="w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Dropdown
                </label>
                <select className="w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Choose an option</option>
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
            </div>
          </div>

          {/* Navigation Components */}
          <div className="bg-white rounded-lg p-4 md:p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Mobile Navigation</h3>
            <div className="space-y-4">
              {/* Bottom Tab Bar Example */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 p-2 text-sm font-medium text-gray-700">
                  Bottom Tab Bar
                </div>
                <div className="bg-white border-t border-gray-200">
                  <div className="flex">
                    <button className="flex-1 py-3 px-2 text-center text-blue-600 bg-blue-50">
                      <div className="text-xs">Home</div>
                    </button>
                    <button className="flex-1 py-3 px-2 text-center text-gray-500">
                      <div className="text-xs">Search</div>
                    </button>
                    <button className="flex-1 py-3 px-2 text-center text-gray-500">
                      <div className="text-xs">Profile</div>
                    </button>
                    <button className="flex-1 py-3 px-2 text-center text-gray-500">
                      <div className="text-xs">Settings</div>
                    </button>
                  </div>
                </div>
              </div>

              {/* Hamburger Menu Example */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 p-2 text-sm font-medium text-gray-700">
                  Hamburger Menu
                </div>
                <div className="bg-white p-4">
                  <button className="flex items-center gap-3 w-full text-left py-2">
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    <span>Menu Item 1</span>
                  </button>
                  <button className="flex items-center gap-3 w-full text-left py-2">
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    <span>Menu Item 2</span>
                  </button>
                  <button className="flex items-center gap-3 w-full text-left py-2">
                    <div className="w-6 h-6 bg-gray-300 rounded"></div>
                    <span>Menu Item 3</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Features */}
          <div className="bg-green-50 rounded-lg p-4 md:p-6 border border-green-200">
            <h4 className="text-lg font-semibold text-green-900 mb-2">
              📱 Mobile Features
            </h4>
            <ul className="text-green-800 space-y-1">
              <li>• Touch-friendly sizing (44px minimum touch targets)</li>
              <li>• Responsive breakpoints for all screen sizes</li>
              <li>• Gesture support (swipe, pinch, tap)</li>
              <li>• Native mobile input types</li>
              <li>• Optimized for thumb navigation</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
