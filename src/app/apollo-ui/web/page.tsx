"use client";

import { useRouter } from "next/navigation";

export default function ApolloUIWebPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header with Back Button */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              @apollo/ui - Web Components
            </h1>
            <p className="text-gray-600 mt-1">
              Desktop and browser-optimized components
            </p>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Component Categories */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Form Components</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Input Fields</div>
                  <div className="text-sm text-gray-600">Text inputs, number inputs, search fields</div>
                </div>
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Date Pickers</div>
                  <div className="text-sm text-gray-600">Calendar components and date selectors</div>
                </div>
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Buttons</div>
                  <div className="text-sm text-gray-600">Primary, secondary, and action buttons</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Layout Components</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Sidebar</div>
                  <div className="text-sm text-gray-600">Collapsible navigation sidebar</div>
                </div>
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Cards</div>
                  <div className="text-sm text-gray-600">Content containers and panels</div>
                </div>
                <div className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-gray-900">Modals</div>
                  <div className="text-sm text-gray-600">Dialog boxes and overlays</div>
                </div>
              </div>
            </div>
          </div>

          {/* Live Examples */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Examples</h3>
              <div className="space-y-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sample Input Field
                  </label>
                  <input
                    type="text"
                    placeholder="Enter text here..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="p-4 border border-gray-200 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sample Buttons
                  </label>
                  <div className="flex gap-3">
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                      Primary
                    </button>
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                      Secondary
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
              <h4 className="text-lg font-semibold text-blue-900 mb-2">
                🚀 Coming Soon
              </h4>
              <p className="text-blue-800">
                Interactive component playground with live code examples, 
                customization options, and copy-to-clipboard functionality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
