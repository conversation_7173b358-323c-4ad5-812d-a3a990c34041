"use client";

import { useRouter } from "next/navigation";

export default function ApolloUIPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          @apollo/ui Components
        </h1>
        <p className="text-gray-600 mb-8">
          Choose your platform to explore Apollo UI components and examples.
        </p>

        {/* Platform Selection Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl">
          {/* Web Button */}
          <button
            onClick={() => router.push('/apollo-ui/web')}
            className="group px-8 py-6 rounded-xl font-semibold text-center transition-all duration-300 border-2 bg-white text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400 hover:shadow-xl hover:transform hover:scale-105"
          >
            <div className="flex flex-col items-center space-y-3">
              {/* Web Icon */}
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <svg
                  className="w-6 h-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                  />
                </svg>
              </div>
              <div>
                <div className="text-xl font-bold">Web Components</div>
                <div className="text-sm opacity-90 mt-1">Desktop & Browser</div>
              </div>
            </div>
          </button>

          {/* Mobile Button */}
          <button
            onClick={() => router.push('/apollo-ui/mobile')}
            className="group px-8 py-6 rounded-xl font-semibold text-center transition-all duration-300 border-2 bg-white text-green-600 border-green-300 hover:bg-green-50 hover:border-green-400 hover:shadow-xl hover:transform hover:scale-105"
          >
            <div className="flex flex-col items-center space-y-3">
              {/* Mobile Icon */}
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <div className="text-xl font-bold">Mobile Components</div>
                <div className="text-sm opacity-90 mt-1">Touch & Responsive</div>
              </div>
            </div>
          </button>
        </div>

        {/* Additional Info */}
        <div className="mt-12 p-6 bg-white rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            About @apollo/ui
          </h3>
          <p className="text-gray-600 leading-relaxed">
            Apollo UI is a modern React component library that provides both web and mobile-optimized components.
            Each platform offers specialized components designed for the best user experience on that device type.
          </p>
        </div>
      </div>
    </div>
  );
}